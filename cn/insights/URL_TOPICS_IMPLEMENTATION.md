# URL _topics 参数功能实现

## 功能概述

实现了从URL中获取 `_topics` 参数，并根据该参数：
1. 在API请求时带上 `topic` 字段进行筛选
2. 设置分类下拉框默认选中对应的主题

## 修改的文件

### 1. `js/api.js`
- **新增函数**: `getUrlParameter(name)` - 获取URL参数
- **修改函数**: `fetchArticles(page, pageSize, topic)` - 支持topic参数
- **修改函数**: `loadArticles(topicsPromise, page, topic)` - 支持topic参数
- **修改函数**: `initializePage()` - 检查URL参数并应用筛选

### 2. `js/render.js`
- **修改函数**: `setupFacetWP()` - 根据URL参数设置下拉框默认选中
- **新增逻辑**: 在setupFacetWP后设置当前选择的主题状态

## 使用方法

### URL格式
```
https://example.com/cn/insights/?_topics=主题值
```

### 示例
```
https://example.com/cn/insights/?_topics=data-security
https://example.com/cn/insights/?_topics=digital-transformation
https://example.com/cn/insights/?_topics=records-management
```

## 工作流程

1. **页面加载时**:
   - `initializePage()` 函数检查URL中的 `_topics` 参数
   - 如果存在该参数，将其保存到 `window.urlTopicParam`
   - 调用 `loadArticles(topics, 1, urlTopic)` 加载筛选后的文章

2. **API请求**:
   - `fetchArticles()` 函数检查topic参数
   - 如果有topic参数，在API URL中添加 `&topic=${topic}`
   - 发送请求到: `/prod-api/api/web/doc/news/list?pageNum=1&pageSize=12&topic=主题值`

3. **下拉框设置**:
   - `setupFacetWP()` 函数生成主题下拉框HTML时
   - 检查 `window.urlTopicParam` 是否匹配当前选项
   - 如果匹配，添加 `selected` 属性
   - 设置 `currentSelectedTopic` 全局变量

## 测试

创建了测试页面 `test-url-params.html` 用于验证功能：
- 显示当前URL参数
- 提供测试链接
- 测试API函数
- 加载主题数据

### 测试步骤
1. 打开 `cn/insights/test-url-params.html`
2. 点击不同的测试按钮
3. 验证URL参数是否正确获取
4. 验证API函数是否正常工作

## 技术细节

### URL参数获取
```javascript
function getUrlParameter(name) {
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get(name)
}
```

### API请求修改
```javascript
function fetchArticles(page = 1, pageSize = 12, topic = null) {
  let url = `/prod-api/api/web/doc/news/list?pageNum=${page}&pageSize=${actualPageSize}`
  
  if (topic) {
    url += `&topic=${encodeURIComponent(topic)}`
  }
  
  return fetch(url)
}
```

### 下拉框默认选中
```javascript
topics.map((topic) => {
  const isSelected = window.urlTopicParam === topic.typeValue ? ' selected' : ''
  return `<option value="${topic.typeValue}"${isSelected}>${topic.typeLabel}</option>`
})
```

## 注意事项

1. URL参数名使用 `_topics` (带下划线)
2. API请求参数名使用 `topic` (不带下划线)
3. 参数值需要与主题数据中的 `typeValue` 匹配
4. 支持URL编码的参数值
5. 保持与现有搜索和分页功能的兼容性
