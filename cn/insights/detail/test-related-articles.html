<!DOCTYPE html>
<html lang="zh-HANS">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>测试相关新闻动态加载</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .crw-latest-posts {
            padding: 40px 0;
        }
        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        .header-section h2 {
            margin: 0;
            color: #333;
        }
        .btn-text {
            color: #007bff;
            text-decoration: none;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .col-lg-3 {
            flex: 1;
            min-width: 250px;
        }
        .crw-article-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        .crw-article-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .crw-article-item a {
            text-decoration: none;
            color: inherit;
            display: block;
        }
        .article-top {
            display: block;
        }
        .article-img {
            width: 100%;
            height: 200px;
            background-size: cover;
            background-position: center;
            background-color: #f0f0f0;
            display: block;
        }
        .article-cat {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            font-size: 12px;
            margin: 10px;
            border-radius: 4px;
        }
        .article-title {
            display: block;
            padding: 0 10px 10px;
            font-weight: bold;
            color: #333;
            line-height: 1.4;
        }
        .article-bottom {
            padding: 10px;
            border-top: 1px solid #eee;
        }
        .btn-text {
            color: #007bff;
            font-size: 14px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 4px;
            margin: 20px 0;
        }
        .test-info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-info">
            <h3>测试说明</h3>
            <p>这个页面用于测试相关新闻的动态加载功能。页面加载后会自动调用 fetchArticles API 获取文章数据并动态渲染。</p>
            <p>如果看到文章列表，说明功能正常工作。如果看到错误信息，请检查 API 接口或网络连接。</p>
        </div>

        <section class="crw-latest-posts">
            <div class="header-section">
                <h2>相关新闻与焦点分析</h2>
                <a href="../index.html" class="btn-text">所有新闻 ></a>
            </div>
            <div class="row">
                <div class="loading">正在加载相关新闻...</div>
            </div>
        </section>
    </div>

    <!-- 引入动态数据加载脚本 -->
    <script src="index.js"></script>
</body>
</html>
