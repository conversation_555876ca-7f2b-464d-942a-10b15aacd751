<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL参数测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background-color: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>URL参数功能测试</h1>
    
    <div class="test-section">
        <h2>当前URL参数</h2>
        <div id="current-params" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试链接</h2>
        <p>点击以下链接测试不同的_topics参数：</p>
        <button onclick="testUrl('?_topics=data-security')">测试 data-security</button>
        <button onclick="testUrl('?_topics=digital-transformation')">测试 digital-transformation</button>
        <button onclick="testUrl('?_topics=records-management')">测试 records-management</button>
        <button onclick="testUrl('')">清除参数</button>
    </div>
    
    <div class="test-section">
        <h2>API函数测试</h2>
        <button onclick="testGetUrlParameter()">测试 getUrlParameter 函数</button>
        <div id="api-test-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>模拟主题数据</h2>
        <div id="topics-data" class="result"></div>
        <button onclick="loadTopicsData()">加载主题数据</button>
    </div>

    <script type="module">
        // 导入API函数
        import { getUrlParameter, getDocNewTopic } from './js/api.js';
        
        // 显示当前URL参数
        function displayCurrentParams() {
            const params = new URLSearchParams(window.location.search);
            const paramsDiv = document.getElementById('current-params');
            
            if (params.toString()) {
                let paramsList = [];
                for (let [key, value] of params) {
                    paramsList.push(`${key}: ${value}`);
                }
                paramsDiv.innerHTML = `<strong>当前参数:</strong><br>${paramsList.join('<br>')}`;
                paramsDiv.className = 'result success';
            } else {
                paramsDiv.innerHTML = '没有URL参数';
                paramsDiv.className = 'result';
            }
        }
        
        // 测试URL跳转
        window.testUrl = function(params) {
            const newUrl = window.location.pathname + params;
            window.location.href = newUrl;
        }
        
        // 测试getUrlParameter函数
        window.testGetUrlParameter = function() {
            const resultDiv = document.getElementById('api-test-result');
            try {
                const topicsParam = getUrlParameter('_topics');
                const otherParam = getUrlParameter('test');
                
                resultDiv.innerHTML = `
                    <strong>getUrlParameter('_topics'):</strong> ${topicsParam || 'null'}<br>
                    <strong>getUrlParameter('test'):</strong> ${otherParam || 'null'}
                `;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `<strong>错误:</strong> ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 加载主题数据
        window.loadTopicsData = async function() {
            const resultDiv = document.getElementById('topics-data');
            resultDiv.innerHTML = '正在加载...';
            
            try {
                const topics = await getDocNewTopic();
                let topicsHtml = '<strong>可用主题:</strong><br>';
                topics.forEach(topic => {
                    topicsHtml += `• ${topic.typeLabel} (${topic.typeValue})<br>`;
                });
                resultDiv.innerHTML = topicsHtml;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.innerHTML = `<strong>加载失败:</strong> ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载时显示当前参数
        displayCurrentParams();
        
        // 监听URL变化
        window.addEventListener('popstate', displayCurrentParams);
    </script>
</body>
</html>
